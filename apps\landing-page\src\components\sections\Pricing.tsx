import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { useState, useEffect } from 'react';
import { CheckIcon, StarIcon } from '@heroicons/react/24/solid';
import { useTheme } from '@/themes';

export default function Pricing() {
  const { t } = useTranslation('landing');
  const router = useRouter();
  const { locale } = router;
  const isRTL = locale === 'ar';
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const { currentTheme, themeName, isGoldTheme, isPurpleTheme } = useTheme();

  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Track mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  // Get pricing plans from translation
  const plans = Array.from({ length: 3 }, (_, i) => ({
    name: t(`pricing.plans.${i}.name`),
    price: t(`pricing.plans.${i}.price`),
    currency: t(`pricing.plans.${i}.currency`),
    period: t(`pricing.plans.${i}.period`),
    description: t(`pricing.plans.${i}.description`),
    features: Array.from({ length: 5 }, (_, j) => {
      const feature = t(`pricing.plans.${i}.features.${j}`, { defaultValue: null });
      return feature !== null ? feature : null;
    }).filter(Boolean),
    cta: t(`pricing.plans.${i}.cta`),
    popular: t(`pricing.plans.${i}.popular`) === 'true',
  }));

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  return (
    <section
      id="pricing"
      ref={ref}
      className="relative section-padding overflow-hidden"
      style={{
        background: `
          radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, ${currentTheme.colors.primary[500]}20 0%, transparent 60%),
          ${currentTheme.backgrounds.tertiary || currentTheme.backgrounds.secondary}
        `
      }}
    >
      {/* Enhanced Background with Glass Orbs */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Theme-Aware Glass Orbs */}
        <motion.div
          animate={{
            y: [-20, 20, -20],
            rotate: [0, 180, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 16, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute top-20 right-10 w-28 h-28 rounded-full opacity-12"
          style={{
            background: currentTheme.colors.glass.background,
            backdropFilter: currentTheme.colors.glass.backdropBlur,
            WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
            border: `1px solid ${currentTheme.colors.glass.border}`,
            boxShadow: currentTheme.colors.glass.shadow
          }}
        />

        <motion.div
          animate={{
            y: [30, -30, 30],
            rotate: [360, 180, 0],
            scale: [1.1, 1, 1.1]
          }}
          transition={{ duration: 20, repeat: Infinity, ease: 'easeInOut' }}
          className="absolute bottom-28 left-8 w-36 h-36 rounded-full opacity-10"
          style={{
            background: `${currentTheme.colors.secondary[500]}20`,
            backdropFilter: 'blur(25px)',
            WebkitBackdropFilter: 'blur(25px)',
            border: `1px solid ${currentTheme.colors.secondary[500]}40`,
            boxShadow: `0 8px 32px ${currentTheme.colors.secondary[500]}20`
          }}
        />

        {/* Floating Particles */}
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [-10, 10, -10],
              x: [-5, 5, -5],
              opacity: [0.1, 0.3, 0.1],
              scale: [1, 1.2, 1]
            }}
            transition={{
              duration: 4 + i * 1,
              repeat: Infinity,
              ease: 'easeInOut',
              delay: i * 1
            }}
            className="absolute w-1 h-1 bg-white rounded-full"
            style={{
              left: `${25 + (i * 15)}%`,
              top: `${35 + (i * 12)}%`,
              filter: 'blur(0.5px)'
            }}
          />
        ))}
      </div>
      <div className="container mx-auto container-padding">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mb-16"
        >
          {/* Enhanced Section Header with Premium Typography */}
          <motion.div
            variants={itemVariants}
            className="relative mb-8"
          >


            <motion.h2
              variants={itemVariants}
              className={`heading-lg mb-4 text-arabic-premium ${
                isRTL
                  ? 'font-cairo text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight'
                  : 'font-display text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight'
              }`}
              style={{
                background: currentTheme.gradients.text,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
                filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2))'
              }}
            >
              {t('pricing.title')}
            </motion.h2>
          </motion.div>

          <motion.p
            variants={itemVariants}
            className={`text-xl lg:text-2xl max-w-4xl mx-auto mb-6 leading-relaxed ${
              isRTL ? 'font-tajawal' : 'font-sans'
            }`}
            style={{
              color: currentTheme.colors.text.secondary,
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
            }}
          >
            {t('pricing.subtitle')}
          </motion.p>
          <motion.p
            variants={itemVariants}
            className={`text-base ${isRTL ? 'font-tajawal' : 'font-sans'}`}
            style={{ color: currentTheme.colors.text.muted }}
          >
            {t('pricing.note')}
          </motion.p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`relative group overflow-hidden ${
                plan.popular
                  ? 'scale-105 lg:scale-110'
                  : 'hover:scale-105'
              } transition-all duration-500`}
              style={{
                background: plan.popular
                  ? `${currentTheme.colors.glass.background}, ${currentTheme.gradients.card}`
                  : currentTheme.gradients.card,
                backdropFilter: currentTheme.colors.glass.backdropBlur,
                WebkitBackdropFilter: currentTheme.colors.glass.backdropBlur,
                border: plan.popular
                  ? `2px solid ${currentTheme.colors.glass.border}`
                  : `1px solid ${currentTheme.colors.glass.border}`,
                borderRadius: '20px',
                padding: '2rem',
                boxShadow: plan.popular
                  ? currentTheme.shadows.premium
                  : currentTheme.shadows.glass,
              }}
              whileHover={{
                y: -8,
                transition: { duration: 0.3 }
              }}
            >
              {/* Enhanced Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 z-10">
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                    className="relative"
                    style={{
                      background: currentTheme.gradients.primary,
                      padding: '8px 20px',
                      borderRadius: '25px',
                      boxShadow: currentTheme.shadows.lg,
                      border: `1px solid ${currentTheme.colors.glass.border}`
                    }}
                  >
                    <div className="flex items-center gap-2 text-white font-semibold text-sm">
                      <StarIcon className="w-4 h-4" />
                      <span className={isRTL ? 'font-cairo' : 'font-sans'}>
                        {isRTL ? 'الأكثر شعبية' : 'Most Popular'}
                      </span>
                    </div>
                    {/* Shimmer effect */}
                    <div
                      className="absolute inset-0 rounded-full opacity-30"
                      style={{
                        background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%)',
                        animation: 'glassShimmer 2s ease-in-out infinite'
                      }}
                    />
                  </motion.div>
                </div>
              )}

              {/* Syrian Cultural Accent for Popular Plan */}
              {plan.popular && (
                <div className="absolute top-4 right-4 flex space-x-1 rtl:space-x-reverse opacity-40">
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="w-2 h-8 bg-gold-500 rounded-full shadow-lg"
                  />
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                    className="w-2 h-8 bg-gold-600 rounded-full shadow-lg"
                  />
                </div>
              )}

              {/* Enhanced Plan Header */}
              <div className="text-center mb-8 relative">
                <motion.h3
                  className={`heading-sm mb-3 ${
                    isRTL ? 'font-cairo text-2xl lg:text-3xl' : 'font-display text-2xl lg:text-3xl'
                  } font-bold text-white`}
                  style={{
                    textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
                  }}
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  {plan.name}
                </motion.h3>
                <p className={`text-gray-300 mb-6 leading-relaxed ${
                  isRTL ? 'font-tajawal' : 'font-sans'
                }`}>
                  {plan.description}
                </p>

                {/* Enhanced Price Display */}
                <div className="relative">
                  {plan.popular && (
                    <div className="absolute -inset-4 rounded-2xl opacity-20"
                         style={{
                           background: 'linear-gradient(135deg, #CE1126 0%, #007A3D 100%)',
                           filter: 'blur(20px)'
                         }}
                    />
                  )}
                  <div className="relative flex items-baseline justify-center gap-2 p-4 rounded-xl"
                       style={{
                         background: plan.popular
                           ? 'rgba(255, 255, 255, 0.1)'
                           : 'rgba(255, 255, 255, 0.05)',
                         backdropFilter: 'blur(10px)',
                         border: '1px solid rgba(255, 255, 255, 0.1)'
                       }}>
                    <span className={`text-4xl lg:text-5xl font-bold text-white ${
                      isRTL ? 'font-cairo' : 'font-display'
                    }`}
                          style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}>
                      {plan.price === '0' ? (isRTL ? 'مجاني' : 'Free') : plan.price}
                    </span>
                    {plan.price !== '0' && (
                      <>
                        <span className="text-lg text-gray-300 font-medium">
                          {plan.currency}
                        </span>
                        <span className="text-gray-400">
                          / {plan.period}
                        </span>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Features */}
              <div className="mb-8">
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start gap-3">
                      <CheckIcon className="w-5 h-5 text-success-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-600 dark:text-gray-300">
                        {feature}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Enhanced CTA Button */}
              <motion.button
                type="button"
                className={`relative w-full py-4 px-6 rounded-xl font-semibold transition-all duration-300 overflow-hidden group ${
                  isRTL ? 'font-cairo' : 'font-sans'
                }`}
                style={{
                  background: plan.popular
                    ? 'linear-gradient(135deg, #CE1126 0%, #007A3D 100%)'
                    : 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
                  backdropFilter: 'blur(20px)',
                  border: plan.popular
                    ? '1px solid rgba(255, 255, 255, 0.3)'
                    : '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: plan.popular
                    ? '0 8px 25px rgba(206, 17, 38, 0.3)'
                    : '0 4px 15px rgba(0, 0, 0, 0.1)',
                  color: 'white',
                  textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)'
                }}
                whileHover={{
                  scale: 1.02,
                  y: -2,
                  transition: { duration: 0.2 }
                }}
                whileTap={{ scale: 0.98 }}
              >
                {/* Button shimmer effect */}
                <div
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    background: 'linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%)',
                    animation: 'glassShimmer 1.5s ease-in-out infinite'
                  }}
                />
                <span className="relative z-10">{plan.cta}</span>
              </motion.button>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Additional Info Section */}
        <motion.div
          variants={itemVariants}
          initial="hidden"
          animate={inView ? 'visible' : 'hidden'}
          className="text-center mt-20"
        >
          <div
            className="relative max-w-5xl mx-auto p-8 lg:p-12 rounded-3xl overflow-hidden"
            style={{
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
              backdropFilter: 'blur(25px)',
              WebkitBackdropFilter: 'blur(25px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)'
            }}
          >
            {/* Gold Accent Background Elements */}
            <div className="absolute top-6 right-6 flex space-x-2 rtl:space-x-reverse opacity-20">
              <div className="w-4 h-16 bg-gold-500 rounded-full"></div>
              <div className="w-4 h-16 bg-gold-600 rounded-full"></div>
            </div>

            <motion.h3
              className={`heading-sm mb-6 text-white ${
                isRTL ? 'font-cairo text-2xl lg:text-3xl' : 'font-display text-2xl lg:text-3xl'
              } font-bold`}
              style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)' }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              {isRTL ? 'لديك أسئلة حول الأسعار؟' : 'Questions about pricing?'}
            </motion.h3>
            <motion.p
              className={`text-gray-300 mb-8 text-lg leading-relaxed max-w-3xl mx-auto ${
                isRTL ? 'font-tajawal' : 'font-sans'
              }`}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              {isRTL
                ? 'فريقنا جاهز لمساعدتك في اختيار الخطة المناسبة لاحتياجاتك'
                : 'Our team is ready to help you choose the right plan for your needs'
              }
            </motion.p>
            <motion.div
              className="flex flex-col sm:flex-row items-center justify-center gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <motion.button
                type="button"
                className={`btn-primary px-8 py-4 text-lg ${isRTL ? 'font-cairo' : 'font-sans'}`}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {isRTL ? 'تواصل معنا' : 'Contact Us'}
              </motion.button>
              <motion.button
                type="button"
                className={`btn-outline px-8 py-4 text-lg ${isRTL ? 'font-cairo' : 'font-sans'}`}
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
              >
                {isRTL ? 'جدولة مكالمة' : 'Schedule a Call'}
              </motion.button>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
